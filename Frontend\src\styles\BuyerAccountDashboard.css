.BuyerAccountDashboard {
  display: flex;
  flex-direction: column;
  width: 100%;
}

/* Stats Cards */
.BuyerAccountDashboard .stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--basefont);
  margin-bottom: var(--heading6);
}

.BuyerAccountDashboard .stat-card {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  padding: var(--basefont);
  border-radius: var(--border-radius);
  color: var(--white);
  text-align: center;
  min-height: 120px;
}

.BuyerAccountDashboard .stat-card.downloads {
  background: linear-gradient(90deg, #857a53, #00a0fe);
}

.BuyerAccountDashboard .stat-card.requests {
  background: linear-gradient(90deg, #324e33, #00a0fe);
}

.BuyerAccountDashboard .stat-card.bids {
  background: linear-gradient(90deg, #44313d, #00a0fe);
}

.BuyerAccountDashboard .stat-number {
  font-size: 36px;
  font-weight: 700;
  margin-bottom: var(--extrasmallfont);
}

.BuyerAccountDashboard .stat-label {
  font-size: var(--basefont);
  font-weight: 500;
}

/* Section Styles */
.BuyerAccountDashboard .section {
  margin-bottom: var(--heading6);
}

.BuyerAccountDashboard .section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--basefont);
}

.BuyerAccountDashboard .section-title {
  font-size: var(--heading6);
  color: var(--secondary-color);
  font-weight: 600;
  margin: 0;
}

.BuyerAccountDashboard .view-all {
  color: var(--btn-color);
  font-size: var(--smallfont);
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
}

.BuyerAccountDashboard .view-all:hover {
  text-decoration: underline;
}

/* Table Styles */
.BuyerAccountDashboard .table {
  width: 100%;
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  overflow: hidden;
}

.BuyerAccountDashboard .table-header {
  display: grid;
  grid-template-columns: 0.5fr 1fr 3fr 1.5fr 1fr 1fr 0.5fr;
  background-color: var(--bg-gray);
  padding: var(--smallfont) var(--basefont);
  font-weight: 600;
  color: var(--secondary-color);
  border-bottom: 1px solid var(--light-gray);
}

.BuyerAccountDashboard .table-row {
  display: grid;
  grid-template-columns: 0.5fr 1fr 3fr 1.5fr 1fr 1fr 0.5fr;
  padding: var(--smallfont) var(--basefont);
  border-bottom: 1px solid var(--light-gray);
  align-items: center;
}

.BuyerAccountDashboard .table-row:last-child {
  border-bottom: none;
}

.BuyerAccountDashboard .table-cell {
  padding: 0 var(--extrasmallfont);
  font-size: var(--smallfont);
}

.BuyerAccountDashboard .table-cell.no,
.BuyerAccountDashboard .table-cell.action {
  text-align: center;
}

.BuyerAccountDashboard .content-item {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
}

.BuyerAccountDashboard .content-image {
  width: 50px;
  height: 50px;
  border-radius: var(--border-radius);
  overflow: hidden;
  flex-shrink: 0;
}

.BuyerAccountDashboard .content-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.BuyerAccountDashboard .content-info {
  display: flex;
  flex-direction: column;
}

.BuyerAccountDashboard .content-title {
  font-weight: 500;
  color: var(--text-color);
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
}

.BuyerAccountDashboard .content-coach {
  font-size: var(--extrasmallfont);
  color: var(--dark-gray);
}

.BuyerAccountDashboard .status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: var(--border-radius);
  font-size: var(--extrasmallfont);
  font-weight: 500;
  text-align: center;
}

.BuyerAccountDashboard .status-badge.downloaded {
  background-color: rgba(46, 204, 113, 0.1);
  color: #2ecc71;
}

.BuyerAccountDashboard .status-badge.pending {
  background-color: rgba(243, 156, 18, 0.1);
  color: #f39c12;
}

.BuyerAccountDashboard .status-badge.approved {
  background-color: rgba(52, 152, 219, 0.1);
  color: #3498db;
}

.BuyerAccountDashboard .status-badge.completed {
  background-color: rgba(46, 204, 113, 0.1);
  color: #2ecc71;
}

.BuyerAccountDashboard .status-badge.active {
  background-color: rgba(52, 152, 219, 0.1);
  color: #3498db;
}

.BuyerAccountDashboard .status-badge.won {
  background-color: rgba(46, 204, 113, 0.1);
  color: #2ecc71;
}

.BuyerAccountDashboard .status-badge.lost {
  background-color: rgba(231, 76, 60, 0.1);
  color: #e74c3c;
}

.BuyerAccountDashboard .action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
  color: var(--dark-gray);
  border: none;
  cursor: pointer;
  transition: color 0.3s ease;
  font-size: var(--basefont);
}

.BuyerAccountDashboard .action-btn:hover {
  color: var(--btn-color);
}

/* Responsive styles */
@media (max-width: 992px) {
  .BuyerAccountDashboard .table-header,
  .BuyerAccountDashboard .table-row {
    grid-template-columns: 0.5fr 1fr 2fr 1.5fr 1fr 1fr 0.5fr;
  }

  .BuyerAccountDashboard .content-title {
    max-width: 150px;
  }
}

@media (max-width: 768px) {
  .BuyerAccountDashboard .stats {
    grid-template-columns: 1fr;
    gap: var(--smallfont);
  }

  .BuyerAccountDashboard .stat-card {
    min-height: 100px;
  }

  .BuyerAccountDashboard .table {
    overflow-x: auto;
  }

  .BuyerAccountDashboard .table-header,
  .BuyerAccountDashboard .table-row {
    min-width: 700px;
  }
}
