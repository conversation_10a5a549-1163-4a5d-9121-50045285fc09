/* Signup Page Styles */
.signup__page {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: calc(100vh - var(--navbar-height, 90px));
  padding: var(--spacing-lg, 40px) var(--spacing-md, 20px);
  background-color: var(--bg-gray);
}

.signup__container {
  background-color: var(--white);
  border-radius: var(--border-radius-large);
  box-shadow: var(--box-shadow-light);
  padding: var(--spacing-lg, 40px);
  width: 100%;
  max-width: var(--container-sm, 500px);
}

.signup__title {
  font-size: var(--heading4);
  font-weight: 600;
  color: var(--secondary-color);
  text-align: center;
  margin-bottom: var(--spacing-md, 20px);
}

/* Account Type Selection */
.signup__account-type {
  margin-bottom: var(--spacing-md, 20px);
}

.signup__label {
  font-size: var(--smallfont);
  font-weight: 500;
  color: var(--secondary-color);
  margin-bottom: var(--spacing-xs, 10px);
}

.signup__options {
  display: flex;
  gap: var(--spacing-sm, 15px);
  justify-content: center;
}

.signup__options {
  display: flex;
  gap: var(--spacing-sm, 15px);
  justify-content: center;
  position: relative;
}

.signup__option {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-sm, 15px);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.3s ease;
  width: 50%;
}

.signup__option--selected {
  border-color: var(--btn-color);
  background-color: rgba(238, 52, 37, 0.05);
}

.signup__option-icon {
  font-size: var(--heading5);
  color: var(--secondary-color);
  margin-bottom: var(--spacing-xs, 10px);
}

.signup__option-text {
  font-size: var(--smallfont);
  color: var(--secondary-color);
  text-align: center;
}

.signup__check-circle {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 20px;
  height: 20px;
  background-color: var(--btn-color);
  border-radius: 50%;
  margin-left: var(--spacing-xs, 10px);
  margin-top: calc(var(--spacing-xs, 10px) * -1);
  margin-right: calc(var(--spacing-xs, 10px) * -1);
  align-self: flex-start;
}

.signup__check-icon {
  color: var(--white);
  font-size: 12px;
}

/* Form Styles */
.signup__form {
  display: flex;
  flex-direction: column;
}

.signup__form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-sm, 15px);
  margin-bottom: var(--spacing-xs, 10px);
}

.signup__input-container {
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-md, 20px);
  width: 100%;
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  background-color: var(--white);
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.signup__input-container:focus-within {
  border-color: var(--btn-color);
  box-shadow: 0 0 0 2px rgba(238, 52, 37, 0.1);
}

.signup__input-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  padding-left: var(--spacing-xs, 10px);
  color: var(--dark-gray);
  font-size: var(--basefont);
  min-width: var(--spacing-lg, 40px);
}

.signup__input {
  width: 100%;
  padding: var(--spacing-xs, 10px) var(--spacing-sm, 15px);
  border: none;
  font-size: var(--basefont);
  color: var(--text-color);
  background-color: transparent;
  outline: none;
}

.signup__input:focus {
  border-color: var(--btn-color);
  box-shadow: 0 0 0 2px rgba(238, 52, 37, 0.1);
  outline: none;
}

.signup__input::placeholder {
  color: var(--dark-gray);
  opacity: 0.7;
}

.signup__input--error {
  border-color: #ff3b30;
}

.signup__error {
  color: #ff3b30;
  font-size: var(--extrasmallfont);
  margin-top: var(--spacing-xxs, 5px);
}

/* Phone Input Styles */
.signup__phone-container {
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-md, 20px);
  width: 100%;
}

.signup__phone-wrapper {
  display: flex;
  flex: 1;
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  overflow: hidden;
}

.signup__phone-container:focus-within .signup__phone-wrapper {
  border-color: var(--btn-color);
  box-shadow: 0 0 0 2px rgba(238, 52, 37, 0.1);
}

.signup__country-code {
  padding-right: var(--spacing-xs, 10px);
  background-color: var(--white);
  color: var(--text-color);
  font-size: var(--basefont);
  cursor: pointer;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  border: none;
  border-right: 1px solid var(--light-gray);
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 24 24'%3E%3Cpath fill='%23163351' d='M7 10l5 5 5-5z'/%3E%3C/svg%3E");
  text-align: center;
  background-repeat: no-repeat;
  background-position: right 0px center;
  background-size: 15px;
}

.signup__phone-input {
  flex: 1;
  padding: var(--spacing-xs, 10px) var(--spacing-sm, 15px);
  border: none;
  font-size: var(--basefont);
  color: var(--text-color);
  background-color: transparent;
  outline: none;
}

/* Terms Checkbox */
.signup__terms {
  display: flex;
  align-items: flex-start;
  margin-bottom: var(--spacing-md, 20px);
  position: relative;
}

.signup__checkbox {
  margin-right: var(--spacing-xs, 10px);
  margin-top: 2px;
}

.signup__terms-label {
  font-size: var(--smallfont);
  color: var(--secondary-color);
}

.signup__terms-link {
  color: var(--btn-color);
  text-decoration: none;
}

.signup__terms-link:hover {
  text-decoration: underline;
}

/* Button and Login Link */
.signup__button {
  background-color: var(--btn-color);
  color: var(--white);
  border: none;
  border-radius: var(--border-radius);
  padding: var(--spacing-sm, 15px);
  font-size: var(--basefont);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: var(--spacing-md, 20px);
}

.signup__button:hover {
  background: linear-gradient(
    to bottom,
    var(--primary-color),
    var(--btn-color)
  );
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(238, 52, 37, 0.3);
}

.signup__login-link {
  text-align: center;
  font-size: var(--smallfont);
  color: var(--secondary-color);
}

.signup__link {
  color: var(--btn-color);
  text-decoration: none;
  font-weight: 500;
}

.signup__link:hover {
  text-decoration: underline;
}

/* Responsive styles */
@media (max-width: 768px) {
  .signup__container {
    padding: var(--spacing-md, 20px);
  }

  .signup__form-row {
    grid-template-columns: 1fr;
    gap: 0;
  }
}

@media (max-width: 480px) {
  .signup__page {
    padding: var(--spacing-sm, 15px) var(--spacing-xs, 10px);
  }

  .signup__container {
    padding: var(--spacing-sm, 15px);
  }

  .signup__title {
    font-size: var(--heading5);
  }

  .signup__options {
    flex-direction: column;
    width: 100%;
  }

  .signup__option {
    width: 100%;
    flex-direction: row;
    justify-content: flex-start;
    gap: var(--spacing-sm, 15px);
  }

  .signup__option-icon {
    margin-bottom: 0;
  }
}
