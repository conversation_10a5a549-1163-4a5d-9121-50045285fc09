/* Thank You Page Styles */
.thank-you-page {
  min-height: calc(100vh - 90px);
  background-color: var(--bg-gray);
  padding: 50px 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.thank-you-container {
  width: 100%;
  max-width: 675px;
  padding: 0 20px;
}

/* Success Header */
.success-header {
  text-align: center;
  margin-bottom: 40px;
}

.success-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 64px;
  height: 64px;
  background-color: var(--btn-color);
  border-radius: 50%;
  margin: 0 auto 20px;
}

.success-icon svg {
  font-size: 32px;
  color: var(--white);
}

.success-title {
  font-size: var(--heading4);
  font-weight: 600;
  color: var(--secondary-color);
  margin-bottom: 10px;
}

.success-message {
  font-size: var(--basefont);
  color: var(--dark-gray);
  max-width: 400px;
  margin: 0 auto;
  line-height: 1.5;
}

/* Order Information Card */
.order-info-card {
  background-color: var(--white);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius-large);
  padding: 40px;
  margin-bottom: 30px;
}

.order-info-title {
  font-size: var(--heading6);
  font-weight: 600;
  color: var(--secondary-color);
  margin-bottom: 30px;
  text-align: left;
}

/* Order Details Grid */
.order-details-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  margin-bottom: 30px;
  padding-bottom: 30px;
  border-bottom: 1px solid var(--light-gray);
}

.order-detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
}

.detail-label {
  font-size: var(--basefont);
  color: var(--dark-gray);
  font-weight: 400;
}

.detail-value {
  font-size: var(--basefont);
  color: var(--text-color);
  font-weight: 600;
}

/* Details Section */
.details-section {
  display: grid;
  grid-template-columns: 1fr 1px 1fr;
  gap: 30px;
  margin-bottom: 30px;
  padding-bottom: 30px;
  border-bottom: 1px solid var(--light-gray);
}

.details-section-divider {
  background-color: var(--light-gray);
  width: 1px;
  height: 100%;
}

.section-title {
  font-size: var(--heading6);
  font-weight: 600;
  color: var(--secondary-color);
  margin-bottom: 20px;
}

.detail-group {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.detail-row {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.detail-row .detail-label {
  font-size: var(--basefont);
  color: var(--dark-gray);
  font-weight: 400;
}

.detail-row .detail-value {
  font-size: var(--basefont);
  color: var(--text-color);
  font-weight: 500;
}

/* Payment Details */
.payment-method {
  display: flex;
  align-items: center;
  gap: 10px;
}

.payment-logo {
  width: 32px;
  height: 20px;
  object-fit: contain;
}

.card-number {
  font-size: var(--basefont);
  color: var(--text-color);
  font-weight: 500;
}

/* Item Information */
.item-info-section {
  margin-bottom: 40px;
}

.item-info-content {
  display: flex;
  align-items: flex-start;
  gap: 20px;
  padding: 20px;
  background-color: var(--white);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
}

.item-image {
  flex-shrink: 0;
  width: 80px;
  height: 80px;
}

.product-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: var(--border-radius);
}

.item-details {
  flex: 1;
}

.item-title {
  font-size: var(--basefont);
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 10px;
  line-height: 1.4;
}

.item-category {
  font-size: var(--smallfont);
  color: var(--dark-gray);
  margin: 0;
  font-weight: 400;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 20px;
  justify-content: center;
  align-items: center;
}

.action-buttons .btn {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 15px 30px;
  border-radius: var(--border-radius);
  font-size: var(--basefont);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  text-decoration: none;
  min-width: 140px;
  justify-content: center;
}

.btn-icon {
  font-size: 16px;
}

.download-btn {
  background-color: transparent;
  color: var(--btn-color);
  border: 1px solid var(--btn-color);
}

.download-btn:hover {
  background-color: var(--btn-color);
  color: var(--white);
  transform: translateY(-2px);
  box-shadow: var(--box-shadow);
}

.download-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.download-btn:disabled:hover {
  background-color: transparent;
  color: var(--btn-color);
  transform: none;
}

.homepage-btn {
  background-color: var(--btn-color);
  color: var(--white);
}

.homepage-btn:hover {
  background: linear-gradient(
    to bottom,
    var(--primary-color),
    var(--btn-color)
  );
  transform: translateY(-2px);
  box-shadow: var(--box-shadow);
}

/* Spinning Animation */
.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .thank-you-page {
    padding: 30px 0;
  }

  .thank-you-container {
    padding: 0 15px;
  }

  .order-info-card {
    padding: 30px;
  }

  .order-details-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .details-section {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .details-section-divider {
    display: none;
  }

  .item-info-content {
    flex-direction: column;
    text-align: center;
    align-items: center;
  }

  .action-buttons {
    flex-direction: column;
    gap: 15px;
  }

  .action-buttons .btn {
    width: 100%;
    justify-content: center;
  }

  .success-icon {
    width: 56px;
    height: 56px;
  }

  .success-icon svg {
    font-size: 28px;
  }

  .success-title {
    font-size: var(--heading5);
  }
}

@media (max-width: 480px) {
  .order-info-card {
    padding: 20px;
  }

  .item-info-content {
    padding: 15px;
  }

  .item-image {
    width: 60px;
    height: 60px;
  }

  .success-icon {
    width: 48px;
    height: 48px;
  }

  .success-icon svg {
    font-size: 24px;
  }

  .success-title {
    font-size: var(--heading6);
  }

  .success-message {
    font-size: var(--smallfont);
  }
}
