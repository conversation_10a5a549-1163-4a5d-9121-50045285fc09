import { createSlice } from '@reduxjs/toolkit';

// Initial state for the contact form
const initialState = {
  // Form data
  formData: {
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    message: ''
  },
  
  // Form validation errors
  errors: {},
  
  // Form submission state
  isSubmitting: false,
  isSubmitted: false,
  submitError: null,
  
  // Contact information
  contactInfo: {
    email: '<EMAIL>',
    socialLinks: {
      facebook: '#',
      linkedin: '#',
      twitter: '#'
    }
  }
};

const contactSlice = createSlice({
  name: 'contact',
  initialState,
  reducers: {
    // Update form field
    updateFormField: (state, action) => {
      const { field, value } = action.payload;
      state.formData[field] = value;
      
      // Clear error for this field when user starts typing
      if (state.errors[field]) {
        delete state.errors[field];
      }
    },
    
    // Set form errors
    setFormErrors: (state, action) => {
      state.errors = action.payload;
    },
    
    // Clear form errors
    clearFormErrors: (state) => {
      state.errors = {};
    },
    
    // Set form submission state
    setSubmitting: (state, action) => {
      state.isSubmitting = action.payload;
    },
    
    // Set form submission success
    setSubmissionSuccess: (state) => {
      state.isSubmitted = true;
      state.isSubmitting = false;
      state.submitError = null;
      // Reset form data
      state.formData = {
        firstName: '',
        lastName: '',
        email: '',
        phone: '',
        message: ''
      };
    },
    
    // Set form submission error
    setSubmissionError: (state, action) => {
      state.isSubmitting = false;
      state.submitError = action.payload;
    },
    
    // Reset form
    resetForm: (state) => {
      state.formData = {
        firstName: '',
        lastName: '',
        email: '',
        phone: '',
        message: ''
      };
      state.errors = {};
      state.isSubmitted = false;
      state.submitError = null;
    },
    
    // Reset submission state
    resetSubmissionState: (state) => {
      state.isSubmitted = false;
      state.submitError = null;
    }
  }
});

export const {
  updateFormField,
  setFormErrors,
  clearFormErrors,
  setSubmitting,
  setSubmissionSuccess,
  setSubmissionError,
  resetForm,
  resetSubmissionState
} = contactSlice.actions;

export default contactSlice.reducer;
